package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
public class CardCollectionMapper {

    public CardCollectionDto.CardCollectionResponse toCollectionResponse(CardCollection collection) {
        return new CardCollectionDto.CardCollectionResponse(
                collection.getId(),
                collection.getName(),
                collection.getStartDate(),
                collection.getEndDate(),
                collection.getStatus(),
                collection.getSortOrder(),
                collection.getCards().stream()
                        .sorted(Comparator.comparingInt(Card::getSortOrder).thenComparingInt(Card::getId))
                        .map(this::toCardResponse)
                        .collect(Collectors.toList()),
                collection.getRewards().stream()
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardCollectionDto.CardResponse toCardResponse(Card card) {
        return new CardCollectionDto.CardResponse(
                card.getId(),
                card.getName(),
                card.getImageUrl(),
                card.getRarityLevel(),
                card.getStartDate(),
                card.getEndDate(),
                card.getStatus(),
                card.getSortOrder(),
                card.getRewards().stream()
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardCollectionDto.RewardResponse toRewardResponse(Reward reward) {
        String targetType = reward.isForCollection() ? "COLLECTION" : "CARD";
        Integer targetId = reward.getTargetId();
        String targetName = reward.getTargetName();

        return new CardCollectionDto.RewardResponse(
                reward.getId(),
                reward.getRewardType(),
                reward.getMilestonePercentage(),
                reward.getRewardData(),
                targetType,
                targetId,
                targetName
        );
    }
//
//    public List<CardCollectionDto.CardResponse> toCardResponseList(List<Card> cards) {
//        return cards.stream()
//                .map(this::toCardResponse)
//                .collect(Collectors.toList());
//    }
//
//    public List<CardCollectionDto.CardResponse> toCardResponseList(List<Card> cards) {
//        return cards.stream()
//                .map(this::toCardResponse)
//                .collect(Collectors.toList());
//    }
}
