package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CardCollectionRepository extends JpaRepository<CardCollection, Integer> {

    List<CardCollection> findByStatus(CardCollection.CollectionStatus status);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'ENABLED' " +
           "AND cc.startDate <= :now " +
           "AND (cc.endDate IS NULL OR cc.endDate > :now)")
    List<CardCollection> findActiveCollections(@Param("now") LocalDateTime now);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'ENABLED' " +
           "AND cc.startDate <= :now " +
           "AND (cc.endDate IS NULL OR cc.endDate > :now)")
    Page<CardCollection> findActiveCollections(@Param("now") LocalDateTime now, Pageable pageable);

    Page<CardCollection> findByStatus(CardCollection.CollectionStatus status, Pageable pageable);

    List<CardCollection> findByNameIgnoreCase(String name);

    @Query("""
        SELECT cc FROM CardCollection cc
        WHERE LOWER(cc.name) = LOWER(:name)
        AND cc.id <> :excludeId
    """)
    List<CardCollection> findByNameIgnoreCaseExcludingId(@Param("name") String name, @Param("excludeId") Integer excludeId);

//    @Query("SELECT cc FROM CardCollection cc WHERE LOWER(cc.name) = LOWER(:name)")
//    List<CardCollection> findByNameIgnoreCase(@Param("name") String name);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    @Query("SELECT DISTINCT cc FROM CardCollection cc WHERE cc.id = :id")
    Optional<CardCollection> findByIdWithCardsAndRewards(@Param("id") Integer id);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    @Query("SELECT DISTINCT cc FROM CardCollection cc")
    Page<CardCollection> findAllWithCardsAndRewards(Pageable pageable);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    @Query("""
        SELECT DISTINCT cc FROM CardCollection cc
        WHERE (:status IS NULL OR cc.status = :status)
        AND (:startDateFrom IS NULL OR cc.startDate >= :startDateFrom)
        AND (:startDateTo IS NULL OR cc.startDate <= :startDateTo)
        AND (:endDateFrom IS NULL OR cc.endDate >= :endDateFrom)
        AND (:endDateTo IS NULL OR cc.endDate <= :endDateTo)
        ORDER BY cc.sortOrder ASC, cc.createdAt ASC
    """)
    Page<CardCollection> findAllWithCardsAndRewardsFiltered(
            @Param("status") CardCollection.CollectionStatus status,
            @Param("startDateFrom") OffsetDateTime startDateFrom,
            @Param("startDateTo") OffsetDateTime startDateTo,
            @Param("endDateFrom") OffsetDateTime endDateFrom,
            @Param("endDateTo") OffsetDateTime endDateTo,
            Pageable pageable);

//    @Query("SELECT cc FROM CardCollection cc LEFT JOIN FETCH cc.cards LEFT JOIN FETCH cc.rewards " +
//           "WHERE cc.id = :id AND cc.status = 'ENABLED' " +
//           "AND cc.startDate <= :now " +
//           "AND (cc.endDate IS NULL OR cc.endDate > :now)")
//    Optional<CardCollection> findActiveCollectionById(@Param("id") Integer id, @Param("now") LocalDateTime now);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'EXPIRED' " +
           "OR (cc.endDate IS NOT NULL AND cc.endDate <= :now)")
    List<CardCollection> findExpiredCollections(@Param("now") LocalDateTime now);

    List<CardCollection> findByStartDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<CardCollection> findByEndDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.endDate IS NOT NULL " +
           "AND cc.endDate BETWEEN :startDate AND :endDate")
    List<CardCollection> findExpiringBetween(@Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);

    List<CardCollection> findByNameContainingIgnoreCase(String name);

    List<CardCollection> findByOrderBySortOrderAscCreatedAtAsc();

    @Query("SELECT COUNT(cc) FROM CardCollection cc WHERE cc.status = :status")
    long countByStatus(@Param("status") CardCollection.CollectionStatus status);

    @Query("SELECT cc FROM CardCollection cc LEFT JOIN FETCH cc.cards WHERE cc.id = :id")
    CardCollection findByIdWithCards(@Param("id") Integer id);

    @Query("SELECT cc FROM CardCollection cc LEFT JOIN FETCH cc.rewards WHERE cc.id = :id")
    CardCollection findByIdWithRewards(@Param("id") Integer id);

//    @Query("SELECT cc FROM CardCollection cc " +
//           "LEFT JOIN FETCH cc.cards c " +
//           "LEFT JOIN FETCH cc.rewards r " +
//           "WHERE cc.id = :id")
//    CardCollection findByIdWithCardsAndRewards(@Param("id") Integer id);
}
