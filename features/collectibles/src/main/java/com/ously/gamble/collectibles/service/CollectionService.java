package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
@Transactional
public class CollectionService {

    private final CardCollectionRepository cardCollectionRepository;
    private final CardRepository cardRepository;

    public CollectionService(CardCollectionRepository cardCollectionRepository, CardRepository cardRepository) {
        this.cardCollectionRepository = cardCollectionRepository;
        this.cardRepository = cardRepository;
    }

    // === COLLECTION OPERATIONS ===

    @Transactional(readOnly = true)
    public Page<CardCollection> findAllWithCardsAndRewards(Pageable pageable) {
        return cardCollectionRepository.findAllWithCardsAndRewards(pageable);
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findAllWithCardsAndRewardsFiltered(
            CardCollection.CollectionStatus status,
            OffsetDateTime startDateFrom,
            OffsetDateTime startDateTo,
            OffsetDateTime endDateFrom,
            OffsetDateTime endDateTo,
            Pageable pageable) {
        return cardCollectionRepository.findAllWithCardsAndRewardsFiltered(
                status, startDateFrom, startDateTo, endDateFrom, endDateTo, pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
        return cardCollectionRepository.findByIdWithCardsAndRewards(id);
    }
//
//    @Transactional(readOnly = true)
//    public Page<CardCollection> findActiveCollections(Pageable pageable) {
//        return cardCollectionRepository.findActiveCollections(LocalDateTime.now(), pageable);
//    }
//
//    @Transactional(readOnly = true)
//    public Optional<CardCollection> findActiveCollectionById(Integer id) {
//        return cardCollectionRepository.findActiveCollectionById(id, LocalDateTime.now());
//    }

    public CardCollection create(CardCollectionDto.CreateCardCollectionRequest request) throws CollectiblesLogicException {
        CardCollection collection = new CardCollection();

        if (!cardCollectionRepository.findByNameIgnoreCase(request.name()).isEmpty()) {
            throw new CollectiblesLogicException("Collection with this name already exists");
        }

        collection.setName(request.name());

        if (request.startDate() != null) {
            collection.setStartDate(request.startDate());
        } else {
            collection.setStartDate(OffsetDateTime.now());
        }

        if (request.endDate() != null) {
            collection.setEndDate(request.endDate());
        }

        if (collection.getEndDate() != null && collection.getStartDate().isAfter(collection.getEndDate())) {
            throw new CollectiblesLogicException("End date must be after start date");
        }

        collection.setStatus(CardCollection.CollectionStatus.DISABLED);
        collection.setSortOrder(request.sortOrder() != null ? request.sortOrder() : 99);

        return cardCollectionRepository.save(collection);
    }

    public Optional<CardCollection> update(Integer id, CardCollectionDto.UpdateCardCollectionRequest request) throws CollectiblesLogicException {
        CardCollection collection = cardCollectionRepository.findById(id).orElse(null);

        if (collection == null) {
            return Optional.empty();
        }

        if (request.sortOrder() != null) collection.setSortOrder(request.sortOrder());

        if (request.name() != null) {
            if (!cardCollectionRepository.findByNameIgnoreCaseExcludingId(request.name(), id).isEmpty()) {
                throw new CollectiblesLogicException("Collection with this name already exists");
            }

            collection.setName(request.name());
        }

        if (request.status() != null) {
            if (request.status().equals(CardCollection.CollectionStatus.ENABLED) && collection.getCards().isEmpty()) {
                throw new CollectiblesLogicException("Cannot activate collection without cards");
            }
            collection.setStatus(request.status());
        }

        if (request.startDate() != null) {
            collection.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            collection.setEndDate(request.endDate());
        }

        if (collection.getEndDate() != null && collection.getStartDate().isAfter(collection.getEndDate())) {
            throw new CollectiblesLogicException("End date must be after start date");
        }

        return Optional.of(cardCollectionRepository.save(collection));
    }

    public boolean delete(Integer id) {
        if (cardCollectionRepository.existsById(id)) {
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }
//
//    // === CARD OPERATIONS ===
//
//    public CardCollection createCard(Integer collectionId, CardCollectionDto.CreateCardRequest request) {
//        CardCollection collection = cardCollectionRepository.findById(collectionId)
//                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
//
//        Card card = new Card();
//        card.setCardCollection(collection);
//        card.setName(request.name());
//        card.setImageUrl(request.imageUrl());
//        card.setRarityLevel(request.rarityLevel());
//        card.setStartDate(request.startDate());
//        card.setEndDate(request.endDate());
//        card.setSortOrder(request.sortOrder());
//        card.setStatus(Card.CardStatus.DISABLED);
//
//        cardRepository.save(card);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection updateCard(Integer collectionId, Integer cardId, CardCollectionDto.UpdateCardRequest request) {
//        Card card = cardRepository.findById(cardId)
//                .orElseThrow(() -> new IllegalArgumentException("Card not found: " + cardId));
//
//        if (!card.getCardCollection().getId().equals(collectionId)) {
//            throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//        }
//
//        if (request.name() != null) card.setName(request.name());
//        if (request.imageUrl() != null) card.setImageUrl(request.imageUrl());
//        if (request.rarityLevel() != null) card.setRarityLevel(request.rarityLevel());
//        if (request.startDate() != null) card.setStartDate(request.startDate());
//        if (request.endDate() != null) card.setEndDate(request.endDate());
//        if (request.status() != null) card.setStatus(request.status());
//        if (request.sortOrder() != null) card.setSortOrder(request.sortOrder());
//
//        cardRepository.save(card);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection deleteCard(Integer collectionId, Integer cardId) {
//        Card card = cardRepository.findById(cardId)
//                .orElseThrow(() -> new IllegalArgumentException("Card not found: " + cardId));
//
//        if (!card.getCardCollection().getId().equals(collectionId)) {
//            throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//        }
//
//        cardRepository.deleteById(cardId);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    // === REWARD OPERATIONS ===
//
//    public CardCollection createReward(Integer collectionId, CardCollectionDto.CreateRewardRequest request) {
//        CardCollection collection = cardCollectionRepository.findById(collectionId)
//                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
//
//        Reward reward = new Reward();
//
//        if (request.cardId() != null) {
//            Card card = cardRepository.findById(request.cardId())
//                    .orElseThrow(() -> new IllegalArgumentException("Card not found: " + request.cardId()));
//            if (!card.getCardCollection().getId().equals(collectionId)) {
//                throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//            }
//            reward.setCard(card);
//        } else {
//            reward.setCardCollection(collection);
//        }
//
//        reward.setRewardType(request.rewardType());
//        reward.setMilestonePercentage(request.milestonePercentage());
//        reward.setRewardData(request.rewardData());
//
//        rewardRepository.save(reward);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection updateReward(Integer collectionId, Integer rewardId, CardCollectionDto.UpdateRewardRequest request) {
//        Reward reward = rewardRepository.findById(rewardId)
//                .orElseThrow(() -> new IllegalArgumentException("Reward not found: " + rewardId));
//
//        // Verify reward belongs to collection
//        boolean belongsToCollection = (reward.getCardCollection() != null && reward.getCardCollection().getId().equals(collectionId)) ||
//                                    (reward.getCard() != null && reward.getCard().getCardCollection().getId().equals(collectionId));
//
//        if (!belongsToCollection) {
//            throw new IllegalArgumentException("Reward does not belong to collection: " + collectionId);
//        }
//
//        if (request.milestonePercentage() != null) {
//            reward.setMilestonePercentage(request.milestonePercentage());
//        }
//        if (request.rewardData() != null) {
//            reward.setRewardData(request.rewardData());
//        }
//
//        rewardRepository.save(reward);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection deleteReward(Integer collectionId, Integer rewardId) {
//        Reward reward = rewardRepository.findById(rewardId)
//                .orElseThrow(() -> new IllegalArgumentException("Reward not found: " + rewardId));
//
//        // Verify reward belongs to collection
//        boolean belongsToCollection = (reward.getCardCollection() != null && reward.getCardCollection().getId().equals(collectionId)) ||
//                                    (reward.getCard() != null && reward.getCard().getCardCollection().getId().equals(collectionId));
//
//        if (!belongsToCollection) {
//            throw new IllegalArgumentException("Reward does not belong to collection: " + collectionId);
//        }
//
//        rewardRepository.deleteById(rewardId);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    // === VALIDATION ===
//
//    private void validateCollectionName(String name, Integer excludeId) {
//        boolean exists = cardCollectionRepository.findByNameIgnoreCase(name.trim()).stream()
//                .anyMatch(c -> excludeId == null || !c.getId().equals(excludeId));
//        if (exists) {
//            throw new IllegalArgumentException("Collection with this name already exists: " + name);
//        }
//    }





    // Пример псевдокода
//    public CardPiece rollCardPiece(long userId, int cardId) {
//        // 1. Получаем все возможные кусочки для карты
//        List<CardPieceConfig> pieceConfigs = cardPieceConfigRepository.findByCardId(cardId);
//        if (pieceConfigs.isEmpty()) {
//            throw new IllegalStateException("No card pieces configured for card: " + cardId);
//        }
//
//        // 2. Генерируем случайное число от 0 до 1
//        double randomValue = random.nextDouble();
//
//        // 3. Определяем редкость на основе случайного числа
//        // Вероятности: 70% для common, 20% для uncommon, 10% для rare
//        int selectedRarity;
//
//        if (randomValue < 0.1) {
//            // rare (10% шанс)
//            selectedRarity = 3;
//        } else if (randomValue < 0.3) {
//            // uncommon (20% шанс)
//            selectedRarity = 2;
//        } else {
//            // common (70% шанс)
//            selectedRarity = 1;
//        }
//
//        // 4. Выбираем случайный кусочек из выбранной редкости
//        List<CardPieceConfig> piecesOfSelectedRarity = pieceConfigs.stream()
//                .filter(p -> p.getRarityLevel() == selectedRarity)
//                .collect(Collectors.toList());
//
//        // Если нет кусочков выбранной редкости, берем кусочки более низкой редкости
//        if (piecesOfSelectedRarity.isEmpty()) {
//            if (selectedRarity == 3) {
//                // Если нет rare, пробуем uncommon
//                piecesOfSelectedRarity = pieceConfigs.stream()
//                        .filter(p -> p.getRarityLevel() == 2)
//                        .collect(Collectors.toList());
//            }
//
//            // Если нет uncommon или если изначально искали uncommon и не нашли
//            if (piecesOfSelectedRarity.isEmpty()) {
//                // Берем common
//                piecesOfSelectedRarity = pieceConfigs.stream()
//                        .filter(p -> p.getRarityLevel() == 1)
//                        .collect(Collectors.toList());
//            }
//
//            // Если вообще нет кусочков нужной редкости, берем любые
//            if (piecesOfSelectedRarity.isEmpty()) {
//                piecesOfSelectedRarity = pieceConfigs;
//            }
//        }
//
//        CardPieceConfig selectedConfig = piecesOfSelectedRarity.get(
//                random.nextInt(piecesOfSelectedRarity.size()));
//
//        // 5. Обновляем количество кусочков у пользователя
//        userCardsPiecesRepository.incrementPieceCount(userId, cardId, selectedConfig.getPieceNumber());
//
//        // 6. Логируем выпадение кусочка
//        cardPieceDropLogRepository.save(new CardPieceDropLog(
//                userId,
//                cardId,
//                selectedConfig.getPieceNumber(),
//                DropSource.LOOTBOX,  // или другой источник
//                null  // дополнительные данные о контексте
//        ));
//
//        return new CardPiece(selectedConfig.getPieceNumber(), selectedConfig.getRarityLevel());
//    }
}
